 /Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/assets/vydeo_logo.png /Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf /Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf /Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag /Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json /Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin /Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json /Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z /Users/<USER>/Documents/augment-projects/youtube_downloader/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json:  /Users/<USER>/Documents/augment-projects/youtube_downloader/pubspec.yaml /Users/<USER>/Documents/augment-projects/youtube_downloader/assets/vydeo_logo.png /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf /Users/<USER>/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf /Users/<USER>/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag /Users/<USER>/Documents/augment-projects/youtube_downloader/.dart_tool/flutter_build/405ee421726b55f17f6a608e2a427867/native_assets.json /Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.14.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/freezed_annotation-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_mobile_ads-5.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.0+1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/simple_sparse_list-0.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/unicode-1.1.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/youtube_explode_dart-2.4.2/LICENSE /Users/<USER>/development/flutter/bin/cache/pkg/sky_engine/LICENSE /Users/<USER>/development/flutter/packages/flutter/LICENSE /Users/<USER>/Documents/augment-projects/youtube_downloader/DOES_NOT_EXIST_RERUN_FOR_WILDCARD366029557