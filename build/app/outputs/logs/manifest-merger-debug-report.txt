-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:20:5-56:19
INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/debug/AndroidManifest.xml
MERGED from [:google_mobile_ads] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/google_mobile_ads/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-13:19
MERGED from [:google_mobile_ads] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/google_mobile_ads/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-13:19
MERGED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.10.2/transforms/fafc57e46ce6619de1b1da19701dac46/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.10.2/transforms/fafc57e46ce6619de1b1da19701dac46/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:70:5-110:19
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:70:5-110:19
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/59d3a1ee9f31642b5439bc886286a893/transformed/jetified-play-services-ads-base-23.6.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/59d3a1ee9f31642b5439bc886286a893/transformed/jetified-play-services-ads-base-23.6.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8fd900909121ef456c43f218d127af94/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8fd900909121ef456c43f218d127af94/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9c6fecc3af8236fdcf7214a7b8a33b20/transformed/jetified-play-services-appset-16.0.1/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9c6fecc3af8236fdcf7214a7b8a33b20/transformed/jetified-play-services-appset-16.0.1/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e565694bd8e7fc6bedaaac67255baf8f/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e565694bd8e7fc6bedaaac67255baf8f/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/cdc862d0332905ee79ec439600095884/transformed/jetified-play-services-measurement-base-20.1.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/cdc862d0332905ee79ec439600095884/transformed/jetified-play-services-measurement-base-20.1.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5568a35fadd1ad6d1c703f16a16e5540/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5568a35fadd1ad6d1c703f16a16e5540/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:30:5-143:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/d64764d3e19c57f00bd96b21f5aea43c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/d64764d3e19c57f00bd96b21f5aea43c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0d9d3675465ff69d847e2f781f20c61/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0d9d3675465ff69d847e2f781f20c61/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/92a2169b5b2674c8bbe10e1b1d80da0f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/92a2169b5b2674c8bbe10e1b1d80da0f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/125edda7ba6445e7de4c188bde073ab9/transformed/room-runtime-2.2.5/AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/125edda7ba6445e7de4c188bde073ab9/transformed/room-runtime-2.2.5/AndroidManifest.xml:24:5-29:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:1:1-68:12
MERGED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:1:1-68:12
INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:google_mobile_ads] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/google_mobile_ads/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-15:12
MERGED from [:webview_flutter_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/webview_flutter_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-14:12
MERGED from [com.google.android.gms:play-services-ads:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/96770765c685e2e0c1ab8d9009285ff6/transformed/jetified-play-services-ads-23.6.0/AndroidManifest.xml:17:1-25:12
MERGED from [:path_provider_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.10.2/transforms/fafc57e46ce6619de1b1da19701dac46/transformed/constraintlayout-2.1.4/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/308a2e77faa557d0bd706972416bde6a/transformed/browser-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/3b224c4d0fbd8d5cdbd7c14809aacae1/transformed/webkit-1.12.1/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:17:1-112:12
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9982c627a8a059647f55944db772d709/transformed/jetified-user-messaging-platform-3.1.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/59d3a1ee9f31642b5439bc886286a893/transformed/jetified-play-services-ads-base-23.6.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8fd900909121ef456c43f218d127af94/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9c6fecc3af8236fdcf7214a7b8a33b20/transformed/jetified-play-services-appset-16.0.1/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e565694bd8e7fc6bedaaac67255baf8f/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6161c7d81ec7f84936152a9e0d9d8abb/transformed/jetified-play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/cdc862d0332905ee79ec439600095884/transformed/jetified-play-services-measurement-base-20.1.2/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5568a35fadd1ad6d1c703f16a16e5540/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c1f85211ae6978d0218d51595a7f7e62/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [androidx.appcompat:appcompat:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/21f508358b7ef6793f647ddb068091fd/transformed/appcompat-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:17:1-145:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/55520e4df2220e27f13f0bbb7467d11a/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/13dd610fda78ecd8ad3daad9b8195d7e/transformed/jetified-activity-1.8.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d6334635279cb43af07c2144a95c2be9/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ddd5e7b38041965968ff0c456e5ef322/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/294fa292f5b2d15ea9a4a7a32547c712/transformed/jetified-appcompat-resources-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd3810ce99ffe1cc26d54f0b91968d35/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/d64764d3e19c57f00bd96b21f5aea43c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:17:1-28:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/38c9892d413ef9a8c58c589bc394888e/transformed/jetified-ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ebeb29d7113d60e59d5e0acf92f847f3/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aa795bea21f1f26d057d80d68caa881e/transformed/jetified-lifecycle-service-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0eede8414d1ee0e5f236daeb1a2e59a/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1b91be3af319ede480d7185430690ee1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e2e21678784b3eef6b33f576b1691a56/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/b3d51a44ab6b56289d4858158a1ad6dd/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c092edbccc16347970ed4f22e8da111a/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c7156a54e8f0502a1f8f57797d8b3e6e/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6ec70d4eb0351866340efe37c2014fef/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f85bb1d55996e5c6bcd395509e391ec9/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a4220b5ee723fba7acc664a2b5c85228/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a955e1aaa27844724e860bd6269b5afd/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0d9d3675465ff69d847e2f781f20c61/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/285b6d6abe2dd4ba9515886e610a7341/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccaaf8ba0ff17d4bbcf5fc9064ae7a64/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/92a2169b5b2674c8bbe10e1b1d80da0f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/125edda7ba6445e7de4c188bde073ab9/transformed/room-runtime-2.2.5/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ff849810dfe34a0ab790c402787c149e/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e4e9d33e8be7718e1e9997a4b8a9b898/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/40f9f352c921aad516dc9608663791df/transformed/sqlite-framework-2.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/763a38afdacf5e229997da4d718ae330/transformed/sqlite-2.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/47506b70b93177473c53f4d5d7e77c91/transformed/jetified-core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c458939f30f1e90860cb5a0c34566e85/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/9e1412f82216e9a99f9f845f4567c1f2/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/debug/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:2:5-51
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:4:5-67
MERGED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:4:5-67
MERGED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:4:5-67
MERGED from [:google_mobile_ads] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/google_mobile_ads/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [:google_mobile_ads] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/google_mobile_ads/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6161c7d81ec7f84936152a9e0d9d8abb/transformed/jetified-play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6161c7d81ec7f84936152a9e0d9d8abb/transformed/jetified-play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:23:5-67
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:4:22-64
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:7:5-8:38
	android:maxSdkVersion
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:8:9-35
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:7:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:9:5-10:38
	android:maxSdkVersion
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:10:9-35
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:9:22-77
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:13:5-14:40
	tools:ignore
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:14:9-37
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:13:22-79
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:17:5-75
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:17:22-72
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:18:5-75
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:18:22-72
queries
ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:62:5-67:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:35:5-68:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:63:9-66:18
action#android.intent.action.PROCESS_TEXT
ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:64:13-72
	android:name
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:64:21-70
data
ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:65:13-50
	android:mimeType
		ADDED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:65:19-48
uses-sdk
INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/debug/AndroidManifest.xml
MERGED from [:google_mobile_ads] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/google_mobile_ads/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:google_mobile_ads] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/google_mobile_ads/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/webview_flutter_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/webview_flutter_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/96770765c685e2e0c1ab8d9009285ff6/transformed/jetified-play-services-ads-23.6.0/AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/96770765c685e2e0c1ab8d9009285ff6/transformed/jetified-play-services-ads-23.6.0/AndroidManifest.xml:21:5-23:52
MERGED from [:path_provider_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.10.2/transforms/fafc57e46ce6619de1b1da19701dac46/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.10.2/transforms/fafc57e46ce6619de1b1da19701dac46/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/308a2e77faa557d0bd706972416bde6a/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/308a2e77faa557d0bd706972416bde6a/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/3b224c4d0fbd8d5cdbd7c14809aacae1/transformed/webkit-1.12.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/3b224c4d0fbd8d5cdbd7c14809aacae1/transformed/webkit-1.12.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:21:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9982c627a8a059647f55944db772d709/transformed/jetified-user-messaging-platform-3.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9982c627a8a059647f55944db772d709/transformed/jetified-user-messaging-platform-3.1.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/59d3a1ee9f31642b5439bc886286a893/transformed/jetified-play-services-ads-base-23.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/59d3a1ee9f31642b5439bc886286a893/transformed/jetified-play-services-ads-base-23.6.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8fd900909121ef456c43f218d127af94/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8fd900909121ef456c43f218d127af94/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9c6fecc3af8236fdcf7214a7b8a33b20/transformed/jetified-play-services-appset-16.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/9c6fecc3af8236fdcf7214a7b8a33b20/transformed/jetified-play-services-appset-16.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e565694bd8e7fc6bedaaac67255baf8f/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e565694bd8e7fc6bedaaac67255baf8f/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6161c7d81ec7f84936152a9e0d9d8abb/transformed/jetified-play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6161c7d81ec7f84936152a9e0d9d8abb/transformed/jetified-play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/cdc862d0332905ee79ec439600095884/transformed/jetified-play-services-measurement-base-20.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/cdc862d0332905ee79ec439600095884/transformed/jetified-play-services-measurement-base-20.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5568a35fadd1ad6d1c703f16a16e5540/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5568a35fadd1ad6d1c703f16a16e5540/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c1f85211ae6978d0218d51595a7f7e62/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c1f85211ae6978d0218d51595a7f7e62/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.appcompat:appcompat:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/21f508358b7ef6793f647ddb068091fd/transformed/appcompat-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/21f508358b7ef6793f647ddb068091fd/transformed/appcompat-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/55520e4df2220e27f13f0bbb7467d11a/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/55520e4df2220e27f13f0bbb7467d11a/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/13dd610fda78ecd8ad3daad9b8195d7e/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/13dd610fda78ecd8ad3daad9b8195d7e/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d6334635279cb43af07c2144a95c2be9/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/d6334635279cb43af07c2144a95c2be9/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ddd5e7b38041965968ff0c456e5ef322/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ddd5e7b38041965968ff0c456e5ef322/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/294fa292f5b2d15ea9a4a7a32547c712/transformed/jetified-appcompat-resources-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/294fa292f5b2d15ea9a4a7a32547c712/transformed/jetified-appcompat-resources-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd3810ce99ffe1cc26d54f0b91968d35/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/fd3810ce99ffe1cc26d54f0b91968d35/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/d64764d3e19c57f00bd96b21f5aea43c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/d64764d3e19c57f00bd96b21f5aea43c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/38c9892d413ef9a8c58c589bc394888e/transformed/jetified-ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/38c9892d413ef9a8c58c589bc394888e/transformed/jetified-ads-adservices-java-1.0.0-beta05/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ebeb29d7113d60e59d5e0acf92f847f3/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ebeb29d7113d60e59d5e0acf92f847f3/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aa795bea21f1f26d057d80d68caa881e/transformed/jetified-lifecycle-service-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/aa795bea21f1f26d057d80d68caa881e/transformed/jetified-lifecycle-service-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0eede8414d1ee0e5f236daeb1a2e59a/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0eede8414d1ee0e5f236daeb1a2e59a/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1b91be3af319ede480d7185430690ee1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/1b91be3af319ede480d7185430690ee1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e2e21678784b3eef6b33f576b1691a56/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e2e21678784b3eef6b33f576b1691a56/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/b3d51a44ab6b56289d4858158a1ad6dd/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/b3d51a44ab6b56289d4858158a1ad6dd/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c092edbccc16347970ed4f22e8da111a/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c092edbccc16347970ed4f22e8da111a/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c7156a54e8f0502a1f8f57797d8b3e6e/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/c7156a54e8f0502a1f8f57797d8b3e6e/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6ec70d4eb0351866340efe37c2014fef/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/6ec70d4eb0351866340efe37c2014fef/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f85bb1d55996e5c6bcd395509e391ec9/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/f85bb1d55996e5c6bcd395509e391ec9/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a4220b5ee723fba7acc664a2b5c85228/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a4220b5ee723fba7acc664a2b5c85228/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a955e1aaa27844724e860bd6269b5afd/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/a955e1aaa27844724e860bd6269b5afd/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0d9d3675465ff69d847e2f781f20c61/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0d9d3675465ff69d847e2f781f20c61/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/285b6d6abe2dd4ba9515886e610a7341/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/285b6d6abe2dd4ba9515886e610a7341/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccaaf8ba0ff17d4bbcf5fc9064ae7a64/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ccaaf8ba0ff17d4bbcf5fc9064ae7a64/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/92a2169b5b2674c8bbe10e1b1d80da0f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/92a2169b5b2674c8bbe10e1b1d80da0f/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/125edda7ba6445e7de4c188bde073ab9/transformed/room-runtime-2.2.5/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/125edda7ba6445e7de4c188bde073ab9/transformed/room-runtime-2.2.5/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ff849810dfe34a0ab790c402787c149e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/ff849810dfe34a0ab790c402787c149e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e4e9d33e8be7718e1e9997a4b8a9b898/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e4e9d33e8be7718e1e9997a4b8a9b898/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/40f9f352c921aad516dc9608663791df/transformed/sqlite-framework-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/40f9f352c921aad516dc9608663791df/transformed/sqlite-framework-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/763a38afdacf5e229997da4d718ae330/transformed/sqlite-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/763a38afdacf5e229997da4d718ae330/transformed/sqlite-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/47506b70b93177473c53f4d5d7e77c91/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/47506b70b93177473c53f4d5d7e77c91/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c458939f30f1e90860cb5a0c34566e85/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/c458939f30f1e90860cb5a0c34566e85/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/9e1412f82216e9a99f9f845f4567c1f2/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/9e1412f82216e9a99f9f845f4567c1f2/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/96770765c685e2e0c1ab8d9009285ff6/transformed/jetified-play-services-ads-23.6.0/AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/debug/AndroidManifest.xml
meta-data#io.flutter.embedded_views_preview
ADDED from [:google_mobile_ads] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/google_mobile_ads/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:9-12:36
	android:value
		ADDED from [:google_mobile_ads] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/google_mobile_ads/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-33
	android:name
		ADDED from [:google_mobile_ads] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/google_mobile_ads/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-61
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6161c7d81ec7f84936152a9e0d9d8abb/transformed/jetified-play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6161c7d81ec7f84936152a9e0d9d8abb/transformed/jetified-play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:25:22-76
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8fd900909121ef456c43f218d127af94/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/8fd900909121ef456c43f218d127af94/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6161c7d81ec7f84936152a9e0d9d8abb/transformed/jetified-play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6161c7d81ec7f84936152a9e0d9d8abb/transformed/jetified-play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:26:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:27:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:27:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:28:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:28:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:27:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:31:9-65
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:38:9-44:18
action#android.intent.action.VIEW
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:39:21-62
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:41:23-71
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:99:13-82
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:107:9-109:62
	android:resource
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:109:13-59
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:108:13-65
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/AndroidManifest.xml:20:19-85
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6161c7d81ec7f84936152a9e0d9d8abb/transformed/jetified-play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6161c7d81ec7f84936152a9e0d9d8abb/transformed/jetified-play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:25:22-65
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5568a35fadd1ad6d1c703f16a16e5540/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5568a35fadd1ad6d1c703f16a16e5540/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5568a35fadd1ad6d1c703f16a16e5540/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0d9d3675465ff69d847e2f781f20c61/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/e0d9d3675465ff69d847e2f781f20c61/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:140:25-85
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/d64764d3e19c57f00bd96b21f5aea43c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/d64764d3e19c57f00bd96b21f5aea43c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/d64764d3e19c57f00bd96b21f5aea43c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:24:13-50
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.blupremium.youtube_downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.blupremium.youtube_downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/125edda7ba6445e7de4c188bde073ab9/transformed/room-runtime-2.2.5/AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/125edda7ba6445e7de4c188bde073ab9/transformed/room-runtime-2.2.5/AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/125edda7ba6445e7de4c188bde073ab9/transformed/room-runtime-2.2.5/AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/125edda7ba6445e7de4c188bde073ab9/transformed/room-runtime-2.2.5/AndroidManifest.xml:26:13-74
