{"logs": [{"outputFile": "com.blupremium.youtube_downloader.app-mergeDebugResources-37:/values-or/values-or.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/5568a35fadd1ad6d1c703f16a16e5540/transformed/jetified-play-services-basement-18.4.0/res/values-or/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4626", "endColumns": "139", "endOffsets": "4761"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/96770765c685e2e0c1ab8d9009285ff6/transformed/jetified-play-services-ads-23.6.0/res/values-or/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,239,287,340,412,490,590,652,773,905,1027,1082,1138,1253,1338,1384,1487,1527,1574,1634,1727,1775", "endColumns": "39,47,52,71,77,99,61,120,131,121,54,55,114,84,45,102,39,46,59,92,47,53", "endOffsets": "238,286,339,411,489,589,651,772,904,1026,1081,1137,1252,1337,1383,1486,1526,1573,1633,1726,1774,1828"}, "to": {"startLines": "58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6308,6352,6404,6461,6537,6619,6723,6789,6914,7050,7176,7235,7295,7414,7503,7553,7660,7704,7755,7819,7916,8159", "endColumns": "43,51,56,75,81,103,65,124,135,125,58,59,118,88,49,106,43,50,63,96,51,57", "endOffsets": "6347,6399,6456,6532,6614,6718,6784,6909,7045,7171,7230,7290,7409,7498,7548,7655,7699,7750,7814,7911,7963,8212"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/res/values-or/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3591,3702,3863,3995,4112,4267,4402,4516,4766,4933,5046,5207,5340,5490,5647,5712,5784", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "3697,3858,3990,4107,4262,4397,4511,4621,4928,5041,5202,5335,5485,5642,5707,5779,5866"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/308a2e77faa557d0bd706972416bde6a/transformed/browser-1.8.0/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,270,383", "endColumns": "109,104,112,108", "endOffsets": "160,265,378,487"}, "to": {"startLines": "54,55,56,57", "startColumns": "4,4,4,4", "startOffsets": "5871,5981,6086,6199", "endColumns": "109,104,112,108", "endOffsets": "5976,6081,6194,6303"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/21f508358b7ef6793f647ddb068091fd/transformed/appcompat-1.2.0/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,829,905,996,1089,1185,1280,1380,1473,1568,1664,1755,1845,1934,2044,2148,2254,2365,2469,2587,2750,2856", "endColumns": "118,109,106,85,103,119,77,75,90,92,95,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,824,900,991,1084,1180,1275,1375,1468,1563,1659,1750,1840,1929,2039,2143,2249,2360,2464,2582,2745,2851,2941"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,79", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,829,905,996,1089,1185,1280,1380,1473,1568,1664,1755,1845,1934,2044,2148,2254,2365,2469,2587,2750,7968", "endColumns": "118,109,106,85,103,119,77,75,90,92,95,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,824,900,991,1084,1180,1275,1375,1468,1563,1659,1750,1840,1929,2039,2143,2249,2360,2464,2582,2745,2851,8053"}}, {"source": "/Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/res/values-or/values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,80", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2856,2959,3061,3164,3269,3370,3472,8058", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "2954,3056,3159,3264,3365,3467,3586,8154"}}]}]}