1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.blupremium.youtube_downloader"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:4:5-67
15-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:4:22-64
16    <!-- Storage permissions for Android versions below 13 (API 33) -->
17    <uses-permission
17-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:7:5-8:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:7:22-78
19        android:maxSdkVersion="32" />
19-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:8:9-35
20    <uses-permission
20-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:9:5-10:38
21        android:name="android.permission.READ_EXTERNAL_STORAGE"
21-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:9:22-77
22        android:maxSdkVersion="32" /> <!-- For Android 11+ (API 30+) if we need to access all files -->
22-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:10:9-35
23    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- For Android 13+ (API 33+) - granular media permissions -->
23-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:13:5-14:40
23-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:13:22-79
24    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
24-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:17:5-75
24-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:17:22-72
25    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
25-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:18:5-75
25-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:18:22-72
26    <!--
27 Required to query activities that can process text, see:
28         https://developer.android.com/training/package-visibility and
29         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
30
31         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
32    -->
33    <queries>
33-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:62:5-67:15
34        <intent>
34-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:63:9-66:18
35            <action android:name="android.intent.action.PROCESS_TEXT" />
35-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:64:13-72
35-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:64:21-70
36
37            <data android:mimeType="text/plain" />
37-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:65:13-50
37-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:65:19-48
38        </intent>
39        <!-- For browser content -->
40        <intent>
40-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:38:9-44:18
41            <action android:name="android.intent.action.VIEW" />
41-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:39:13-65
41-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:39:21-62
42
43            <category android:name="android.intent.category.BROWSABLE" />
43-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:41:13-74
43-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:41:23-71
44
45            <data android:scheme="https" />
45-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:65:13-50
46        </intent> <!-- End of browser content -->
47        <!-- For CustomTabsService -->
48        <intent>
48-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:47:9-49:18
49            <action android:name="android.support.customtabs.action.CustomTabsService" />
49-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:48:13-90
49-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:48:21-87
50        </intent> <!-- End of CustomTabsService -->
51        <!-- For MRAID capabilities -->
52        <intent>
52-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:52:9-56:18
53            <action android:name="android.intent.action.INSERT" />
53-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:53:13-67
53-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:53:21-64
54
55            <data android:mimeType="vnd.android.cursor.dir/event" />
55-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:65:13-50
55-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:65:19-48
56        </intent>
57        <intent>
57-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:57:9-61:18
58            <action android:name="android.intent.action.VIEW" />
58-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:39:13-65
58-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:39:21-62
59
60            <data android:scheme="sms" />
60-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:65:13-50
61        </intent>
62        <intent>
62-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:62:9-66:18
63            <action android:name="android.intent.action.DIAL" />
63-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:63:13-65
63-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:63:21-62
64
65            <data android:path="tel:" />
65-->/Users/<USER>/Documents/augment-projects/youtube_downloader/android/app/src/main/AndroidManifest.xml:65:13-50
66        </intent>
67    </queries>
68
69    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
69-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:25:5-79
69-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:25:22-76
70    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
70-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:26:5-79
70-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:26:22-76
71    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
71-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:27:5-82
71-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:27:22-79
72    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
72-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:28:5-88
72-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:28:22-85
73    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
73-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:29:5-83
73-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:29:22-80
74    <uses-permission android:name="android.permission.WAKE_LOCK" />
74-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6161c7d81ec7f84936152a9e0d9d8abb/transformed/jetified-play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:25:5-68
74-->[com.google.android.gms:play-services-measurement-sdk-api:20.1.2] /Users/<USER>/.gradle/caches/8.10.2/transforms/6161c7d81ec7f84936152a9e0d9d8abb/transformed/jetified-play-services-measurement-sdk-api-20.1.2/AndroidManifest.xml:25:22-65
75    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
75-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:28:5-77
75-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:28:22-74
76
77    <permission
77-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
78        android:name="com.blupremium.youtube_downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
78-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
79        android:protectionLevel="signature" />
79-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
80
81    <uses-permission android:name="com.blupremium.youtube_downloader.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
81-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
81-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
82
83    <application
84        android:name="android.app.Application"
85        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
85-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/8cf478dec41eed746328fa8046755ba2/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
86        android:debuggable="true"
87        android:extractNativeLibs="true"
88        android:icon="@mipmap/launcher_icon"
89        android:label="Vydeo" >
90
91        <!-- AdMob App ID -->
92        <meta-data
93            android:name="com.google.android.gms.ads.APPLICATION_ID"
94            android:value="ca-app-pub-1213994342188306~4888760326" />
95
96        <activity
97            android:name="com.blupremium.youtube_downloader.MainActivity"
98            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
99            android:exported="true"
100            android:hardwareAccelerated="true"
101            android:launchMode="singleTop"
102            android:taskAffinity=""
103            android:theme="@style/LaunchTheme"
104            android:windowSoftInputMode="adjustResize" >
105
106            <!--
107                 Specifies an Android theme to apply to this Activity as soon as
108                 the Android process has started. This theme is visible to the user
109                 while the Flutter UI initializes. After that, this theme continues
110                 to determine the Window background behind the Flutter UI.
111            -->
112            <meta-data
113                android:name="io.flutter.embedding.android.NormalTheme"
114                android:resource="@style/NormalTheme" />
115
116            <intent-filter>
117                <action android:name="android.intent.action.MAIN" />
118
119                <category android:name="android.intent.category.LAUNCHER" />
120            </intent-filter>
121        </activity>
122        <!--
123             Don't delete the meta-data below.
124             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
125        -->
126        <meta-data
127            android:name="flutterEmbedding"
128            android:value="2" />
129        <meta-data
129-->[:google_mobile_ads] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/google_mobile_ads/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:9-12:36
130            android:name="io.flutter.embedded_views_preview"
130-->[:google_mobile_ads] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/google_mobile_ads/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-61
131            android:value="true" />
131-->[:google_mobile_ads] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/google_mobile_ads/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-33
132
133        <activity
133-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
134            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
134-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
135            android:exported="false"
135-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
136            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
136-->[:url_launcher_android] /Users/<USER>/Documents/augment-projects/youtube_downloader/build/url_launcher_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
137        <activity
137-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:73:9-78:43
138            android:name="com.google.android.gms.ads.AdActivity"
138-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:74:13-65
139            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
139-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:75:13-122
140            android:exported="false"
140-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:76:13-37
141            android:theme="@android:style/Theme.Translucent" />
141-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:77:13-61
142
143        <provider
143-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:80:9-85:43
144            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
144-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:81:13-76
145            android:authorities="com.blupremium.youtube_downloader.mobileadsinitprovider"
145-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:82:13-73
146            android:exported="false"
146-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:83:13-37
147            android:initOrder="100" />
147-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:84:13-36
148
149        <service
149-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:87:9-91:43
150            android:name="com.google.android.gms.ads.AdService"
150-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:88:13-64
151            android:enabled="true"
151-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:89:13-35
152            android:exported="false" />
152-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:90:13-37
153
154        <activity
154-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:93:9-97:43
155            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
155-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:94:13-82
156            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
156-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:95:13-122
157            android:exported="false" />
157-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:96:13-37
158        <activity
158-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:98:9-105:43
159            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
159-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:99:13-82
160            android:excludeFromRecents="true"
160-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:100:13-46
161            android:exported="false"
161-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:101:13-37
162            android:launchMode="singleTask"
162-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:102:13-44
163            android:taskAffinity=""
163-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:103:13-36
164            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
164-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:104:13-72
165
166        <property
166-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:107:9-109:62
167            android:name="android.adservices.AD_SERVICES_CONFIG"
167-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:108:13-65
168            android:resource="@xml/gma_ad_services_config" />
168-->[com.google.android.gms:play-services-ads-lite:23.6.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9547bf6183070c58b8350efb34c4abc1/transformed/jetified-play-services-ads-lite-23.6.0/AndroidManifest.xml:109:13-59
169
170        <activity
170-->[com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/AndroidManifest.xml:20:9-22:45
171            android:name="com.google.android.gms.common.api.GoogleApiActivity"
171-->[com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/AndroidManifest.xml:20:19-85
172            android:exported="false"
172-->[com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/AndroidManifest.xml:22:19-43
173            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
173-->[com.google.android.gms:play-services-base:18.0.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/9aff342e705e1aa1f5b055fb1b25f3fc/transformed/jetified-play-services-base-18.0.0/AndroidManifest.xml:21:19-78
174
175        <meta-data
175-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5568a35fadd1ad6d1c703f16a16e5540/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
176            android:name="com.google.android.gms.version"
176-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5568a35fadd1ad6d1c703f16a16e5540/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
177            android:value="@integer/google_play_services_version" />
177-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5568a35fadd1ad6d1c703f16a16e5540/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
178
179        <uses-library
179-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
180            android:name="androidx.window.extensions"
180-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
181            android:required="false" />
181-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
182        <uses-library
182-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
183            android:name="androidx.window.sidecar"
183-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
184            android:required="false" />
184-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/5093ab42d2307deb2d7ac0b7f5718c38/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
185
186        <provider
186-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:31:9-39:20
187            android:name="androidx.startup.InitializationProvider"
187-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:32:13-67
188            android:authorities="com.blupremium.youtube_downloader.androidx-startup"
188-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:33:13-68
189            android:exported="false" >
189-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:34:13-37
190            <meta-data
190-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:36:13-38:52
191                android:name="androidx.work.WorkManagerInitializer"
191-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:37:17-68
192                android:value="androidx.startup" />
192-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:38:17-49
193            <meta-data
193-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
194                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
194-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
195                android:value="androidx.startup" />
195-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/e80aa748ff6540407d8dba61c0d945fe/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
196            <meta-data
196-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
197                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
197-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
198                android:value="androidx.startup" />
198-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
199        </provider>
200
201        <service
201-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:41:9-46:35
202            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
202-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:42:13-88
203            android:directBootAware="false"
203-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:43:13-44
204            android:enabled="@bool/enable_system_alarm_service_default"
204-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:44:13-72
205            android:exported="false" />
205-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:45:13-37
206        <service
206-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:47:9-53:35
207            android:name="androidx.work.impl.background.systemjob.SystemJobService"
207-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:48:13-84
208            android:directBootAware="false"
208-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:49:13-44
209            android:enabled="@bool/enable_system_job_service_default"
209-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:50:13-70
210            android:exported="true"
210-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:51:13-36
211            android:permission="android.permission.BIND_JOB_SERVICE" />
211-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:52:13-69
212        <service
212-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:54:9-59:35
213            android:name="androidx.work.impl.foreground.SystemForegroundService"
213-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:55:13-81
214            android:directBootAware="false"
214-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:56:13-44
215            android:enabled="@bool/enable_system_foreground_service_default"
215-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:57:13-77
216            android:exported="false" />
216-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:58:13-37
217
218        <receiver
218-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:61:9-66:35
219            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
219-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:62:13-88
220            android:directBootAware="false"
220-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:63:13-44
221            android:enabled="true"
221-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:64:13-35
222            android:exported="false" />
222-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:65:13-37
223        <receiver
223-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:67:9-77:20
224            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
224-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:68:13-106
225            android:directBootAware="false"
225-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:69:13-44
226            android:enabled="false"
226-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:70:13-36
227            android:exported="false" >
227-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:71:13-37
228            <intent-filter>
228-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:73:13-76:29
229                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
229-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:74:17-87
229-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:74:25-84
230                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
230-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:75:17-90
230-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:75:25-87
231            </intent-filter>
232        </receiver>
233        <receiver
233-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:78:9-88:20
234            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
234-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:79:13-104
235            android:directBootAware="false"
235-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:80:13-44
236            android:enabled="false"
236-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:81:13-36
237            android:exported="false" >
237-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:82:13-37
238            <intent-filter>
238-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:84:13-87:29
239                <action android:name="android.intent.action.BATTERY_OKAY" />
239-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:85:17-77
239-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:85:25-74
240                <action android:name="android.intent.action.BATTERY_LOW" />
240-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:86:17-76
240-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:86:25-73
241            </intent-filter>
242        </receiver>
243        <receiver
243-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:89:9-99:20
244            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
244-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:90:13-104
245            android:directBootAware="false"
245-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:91:13-44
246            android:enabled="false"
246-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:92:13-36
247            android:exported="false" >
247-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:93:13-37
248            <intent-filter>
248-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:95:13-98:29
249                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
249-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:96:17-83
249-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:96:25-80
250                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
250-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:97:17-82
250-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:97:25-79
251            </intent-filter>
252        </receiver>
253        <receiver
253-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:100:9-109:20
254            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
254-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:101:13-103
255            android:directBootAware="false"
255-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:102:13-44
256            android:enabled="false"
256-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:103:13-36
257            android:exported="false" >
257-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:104:13-37
258            <intent-filter>
258-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:106:13-108:29
259                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
259-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:107:17-79
259-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:107:25-76
260            </intent-filter>
261        </receiver>
262        <receiver
262-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:110:9-121:20
263            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
263-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:111:13-88
264            android:directBootAware="false"
264-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:112:13-44
265            android:enabled="false"
265-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:113:13-36
266            android:exported="false" >
266-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:114:13-37
267            <intent-filter>
267-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:116:13-120:29
268                <action android:name="android.intent.action.BOOT_COMPLETED" />
268-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:117:17-79
268-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:117:25-76
269                <action android:name="android.intent.action.TIME_SET" />
269-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:118:17-73
269-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:118:25-70
270                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
270-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:119:17-81
270-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:119:25-78
271            </intent-filter>
272        </receiver>
273        <receiver
273-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:122:9-131:20
274            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
274-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:123:13-99
275            android:directBootAware="false"
275-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:124:13-44
276            android:enabled="@bool/enable_system_alarm_service_default"
276-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:125:13-72
277            android:exported="false" >
277-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:126:13-37
278            <intent-filter>
278-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:128:13-130:29
279                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
279-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:129:17-98
279-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:129:25-95
280            </intent-filter>
281        </receiver>
282        <receiver
282-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:132:9-142:20
283            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
283-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:133:13-78
284            android:directBootAware="false"
284-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:134:13-44
285            android:enabled="true"
285-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:135:13-35
286            android:exported="true"
286-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:136:13-36
287            android:permission="android.permission.DUMP" >
287-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:137:13-57
288            <intent-filter>
288-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:139:13-141:29
289                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
289-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:140:17-88
289-->[androidx.work:work-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10.2/transforms/319bee7bd3960c0a614b92127a915aa4/transformed/work-runtime-2.7.0/AndroidManifest.xml:140:25-85
290            </intent-filter>
291        </receiver>
292
293        <uses-library
293-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/d64764d3e19c57f00bd96b21f5aea43c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:23:9-25:40
294            android:name="android.ext.adservices"
294-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/d64764d3e19c57f00bd96b21f5aea43c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:24:13-50
295            android:required="false" />
295-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] /Users/<USER>/.gradle/caches/8.10.2/transforms/d64764d3e19c57f00bd96b21f5aea43c/transformed/jetified-ads-adservices-1.0.0-beta05/AndroidManifest.xml:25:13-37
296
297        <receiver
297-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
298            android:name="androidx.profileinstaller.ProfileInstallReceiver"
298-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
299            android:directBootAware="false"
299-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
300            android:enabled="true"
300-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
301            android:exported="true"
301-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
302            android:permission="android.permission.DUMP" >
302-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
303            <intent-filter>
303-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
304                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
304-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
304-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
305            </intent-filter>
306            <intent-filter>
306-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
307                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
307-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
307-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
308            </intent-filter>
309            <intent-filter>
309-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
310                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
310-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
310-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
311            </intent-filter>
312            <intent-filter>
312-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
313                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
313-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
313-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.10.2/transforms/5f7dcf6815bacc3ae7f099ba801348de/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
314            </intent-filter>
315        </receiver>
316
317        <service
317-->[androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/125edda7ba6445e7de4c188bde073ab9/transformed/room-runtime-2.2.5/AndroidManifest.xml:25:9-28:40
318            android:name="androidx.room.MultiInstanceInvalidationService"
318-->[androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/125edda7ba6445e7de4c188bde073ab9/transformed/room-runtime-2.2.5/AndroidManifest.xml:26:13-74
319            android:directBootAware="true"
319-->[androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/125edda7ba6445e7de4c188bde073ab9/transformed/room-runtime-2.2.5/AndroidManifest.xml:27:13-43
320            android:exported="false" />
320-->[androidx.room:room-runtime:2.2.5] /Users/<USER>/.gradle/caches/8.10.2/transforms/125edda7ba6445e7de4c188bde073ab9/transformed/room-runtime-2.2.5/AndroidManifest.xml:28:13-37
321    </application>
322
323</manifest>
